# Markdown转换JSON v3.0 - 分步处理方案

## 概述

本方案将原来的单步骤提示词拆分为5个专门化的步骤，每个步骤专注于特定的转换任务，有效控制了提示词长度并提升了处理质量。

## 方案架构

### 公共规范文档
- `common/common_rules.md` - 通用转换原则和规范
- `common/widget_specs.md` - 控件规范说明
- `examples/` - 处理示例和最佳实践

### 5步处理流程

#### Step 1: 结构识别与基础转换 (200-250行)
- **文件**: `step1_structure_basic.md`
- **职责**: markdown结构解析、TITLE和TEXT控件生成
- **输出**: 基础JSON结构 + 处理元数据
- **重点**: 文档框架建立、分析性内容识别

#### Step 2: 列表与标题处理 (150-200行)
- **文件**: `step2_list_processing.md`
- **职责**: LIST控件生成、标题重复智能处理
- **输出**: 包含LIST控件的完整结构
- **重点**: 标题重复检测与解决

#### Step 3: 数据表格处理 (200-250行)
- **文件**: `step3_table_processing.md`
- **职责**: TABLE控件生成、图表候选标记
- **输出**: 包含TABLE控件的结构 + 图表候选信息
- **重点**: 数据准确性、图表转换准备

#### Step 4: 图表智能转换 (250-300行)
- **文件**: `step4_chart_conversion.md`
- **职责**: TABLE→CHART转换、数据可视化优化
- **输出**: 包含优化图表的完整结构
- **重点**: 智能类型选择、数据连续性处理

#### Step 5: 数据验证与最终优化 (150-200行)
- **文件**: `step5_final_validation.md`
- **职责**: 质量检查、忠实性验证、最终优化
- **输出**: 符合规范的最终DocumentData JSON
- **重点**: 全面质量保证

## 使用方式

### 顺序执行模式
```
原始markdown → Step1 → Step2 → Step3 → Step4 → Step5 → 最终JSON
```

### 数据传递格式
每个步骤通过`processing_metadata`传递处理信息：
```json
{
  "step": 步骤编号,
  "处理统计": "各种计数信息",
  "候选标记": ["待处理项目列表"],
  "processing_notes": "处理说明"
}
```

## 核心优势

### 1. 长度控制
- 每个步骤提示词控制在150-300行
- 公共规范提取，避免重复
- 示例文档独立，按需引用

### 2. 专业化分工
- 每个步骤专注特定任务
- 减少任务冲突和复杂度
- 提升处理质量和准确性

### 3. 质量保证
- 分步验证，逐层质量控制
- 最终步骤专门进行全面验证
- 忠实性检查贯穿全流程

### 4. 维护便利
- 模块化结构，便于单独维护
- 公共规范统一管理
- 示例文档独立更新

## 关键改进点

### 1. 标题重复处理
- Step 2专门处理父子级控件标题重复问题
- 智能检测与省略机制
- 确保文档结构清晰

### 2. 图表智能转换
- Step 4专门处理图表转换优化
- 数据连续性智能检测
- 量级差异自动处理

### 3. 分析内容突出
- Step 1重点识别分析性内容
- BLOCK样式突出显示
- 确保重要内容不遗漏

### 4. 数据忠实性
- 每个步骤都强调忠实性原则
- Step 5进行最终忠实性验证
- 绝不添加原文不存在的内容

## 质量控制机制

### 分层验证
- 每个步骤内部质量检查
- 步骤间数据传递验证
- 最终步骤全面质量保证

### 忠实性保证
- 数据来源追溯验证
- 内容完整性检查
- 虚构内容零容忍

### 格式规范
- 严格遵循控件规范
- JSON格式标准化
- 数据类型准确性

## 使用建议

### 1. 严格按顺序执行
- 不要跳过任何步骤
- 确保数据传递完整
- 验证每步输出质量

### 2. 重视忠实性验证
- 特别关注Step 5的验证结果
- 发现问题及时回溯修正
- 宁缺毋滥，确保质量

### 3. 灵活应用规范
- 根据文档复杂度选择层级深度
- 合理使用控件样式
- 注重用户体验

### 4. 持续优化改进
- 收集处理反馈
- 优化提示词内容
- 更新示例和规范

## 注意事项

1. **数据传递完整性**: 确保每个步骤的输出包含完整的processing_metadata
2. **忠实性优先**: 任何情况下都不能违背内容忠实性原则
3. **格式严格性**: 严格遵循各控件的格式规范要求
4. **质量检查**: 每个步骤都要进行相应的质量检查
5. **错误处理**: 发现问题时优先修复而非删除内容

通过这种分步处理方案，我们有效解决了原有提示词过长的问题，同时提升了转换质量和维护便利性。
