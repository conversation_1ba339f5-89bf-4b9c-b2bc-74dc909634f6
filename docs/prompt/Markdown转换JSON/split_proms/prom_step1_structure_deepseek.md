<----------------------------(optimized_prompt)---------------------------->
# 结构化转换任务指令

## 任务目标
将房地产月度报告Markdown内容转换为标准DocumentData JSON结构，严格遵守内容忠实性原则

## 核心约束
1. **绝对内容忠实**：
   - 禁止添加/删除/修改原始数据
   - 所有输出必须100%可追溯至源文档
   - 加粗标记处理：
     * TITLE/SUBTITLE字段：移除加粗
     * BOARD样式：移除所有加粗
     * PLAIN样式：保留原始加粗
     * TABLE单元格：移除加粗

2. **智能控件识别**：
   - 标题层级映射：
     `# → DOCUMENT`，`## → SECTION`，`### → PARAGRAPH`
   - 列表处理：
     * 强制检测`**标题**：`格式
     * 必须分离标题与内容
     * 数字序号分析内容转BOARD列表

## 关键处理规则

### 列表控件(LIST)处理
```json
{
  "执行条件": "检测到- **标题**：内容格式",
  "必须操作": [
    "提取加粗文本到title字段(移除**标记)",
    "冒号后内容放入content字段",
    "BOARD样式时移除content加粗"
  ],
  "禁止操作": "整体保留**标题**：内容格式"
}
```

### 表格控件(TABLE)处理
```json
{
  "单元格规范": {
    "type": "TEXT|IMAGE|PROGRESS_BAR|CHANGE",
    "content": "移除所有加粗标记"
  },
  "recommended使用": {
    "条件": "对比表格中有明显优势项",
    "上限": "不超过总项30%"
  }
}
```

### BOARD样式触发词
["趋势", "分析", "走势", "解读", "对比", "总结", "要点", "核心", "关键"]

## 转换执行流程
1. 内容扫描：逐段验证源内容对应关系
2. 控件生成：
 - 标题层级序列化（0→1→1.1→1.1.1）
 - 列表项分割检测（强制冒号格式分离）
 - 表格数据校验（列数一致性检查）
3. 样式决策：
   ```mermaid
   graph TD
     A[内容分析] --> B{含BOARD触发词?}
     B -->|是| C[应用BOARD样式]
     B -->|否| D[保持PLAIN样式]
     C --> E[移除所有加粗]
   ```

## 输出规范
```json
{
  "type": "MONTHLY_REPORT",
  "title": "慧芝湖花园3室2厅2卫价值评测报告",
  "widgets": [/* 严格排序控件 */],
  "conversion_metadata": {
    "chart_candidates": ["3.1.1", "3.1.3", "3.2.1"],
    "processing_notes": "完成3处列表标题提取，标记3个图表候选表"
  }
}
```

## 质量检查点
- [ ] 所有加粗标记处理合规
- [ ] 列表项分离率100%
- [ ] 表格单元格无加粗残留
- [ ] BOARD样式使用准确率
- [ ] 序列编号连续无断层
